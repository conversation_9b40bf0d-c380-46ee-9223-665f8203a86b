package src
{
   import flash.display.*;
   import flash.events.*;
   import flash.text.*;
   import flash.geom.*;
   import flash.filters.*;
   import com.hotpoint.braveManIII.Tool.XMLAsset;
   import com.hotpoint.braveManIII.models.common.VT;
   import com.hotpoint.braveManIII.models.equip.Equip;
   import com.hotpoint.braveManIII.models.equip.EquipBaseAttrib;
   import com.hotpoint.braveManIII.models.gem.Attribute;
   import com.hotpoint.braveManIII.models.gem.Gem;
   import com.hotpoint.braveManIII.models.other.Otherobj;
   import com.hotpoint.braveManIII.models.supplies.Supplies;
   import com.hotpoint.braveManIII.repository.achievement.*;
   import com.hotpoint.braveManIII.repository.elves.ElvesFactory;
   import com.hotpoint.braveManIII.repository.equip.EquipFactory;
   import com.hotpoint.braveManIII.repository.gem.GemFactory;
   import com.hotpoint.braveManIII.repository.other.OtherFactory;
   import com.hotpoint.braveManIII.repository.pet.PetFactory;
   import com.hotpoint.braveManIII.repository.petEquip.PetEquipFactory;
   import com.hotpoint.braveManIII.repository.supplies.SuppliesFactory;
   import com.hotpoint.braveManIII.repository.title.TitleFactory;
   import com.hotpoint.braveManIII.views.achPanel.AchData;
   import com.hotpoint.braveManIII.views.itemsPanel.JinHuaPanel;
   import com.hotpoint.braveManIII.views.itemsPanel.JinHuaPanel2;
   import com.hotpoint.braveManIII.views.newMC.NewMC;
   import com.hotpoint.braveManIII.views.petPanel.NewPetPanel;
   import com.hotpoint.braveManIII.views.storagePanel.StoragePanel;
   import com.hotpoint.braveManIII.views.cardPanel.CardPanel;
   import com.hotpoint.braveManIII.views.caiyaoPanel.CaiYaoPanel;
   import com.hotpoint.braveManIII.views.yuekaPanel.YueKa_Interface;
   import com.hotpoint.braveManIII.views.youlingPanel.Panel_youling;
   import com.hotpoint.braveManIII.views.pkPanel.PK_UI;
   import com.hotpoint.braveManIII.views.paiHangPanel.PaiHang_Data;
   import src._data.*;
   
   /**
    * 可爱风格的GM界面
    * 提供友好的用户界面和完整的GM功能
    */
   public class CuteGMPanel extends MovieClip
   {
      // 界面尺寸常量
      private static const PANEL_WIDTH:Number = 800;
      private static const PANEL_HEIGHT:Number = 600;
      private static const TAB_HEIGHT:Number = 50;
      private static const CONTENT_HEIGHT:Number = PANEL_HEIGHT - TAB_HEIGHT - 60;
      
      // 颜色常量
      private static const PINK_LIGHT:uint = 0xFFB6C1;
      private static const PINK_MEDIUM:uint = 0xFFC0CB;
      private static const PINK_DARK:uint = 0xFF69B4;
      private static const WHITE:uint = 0xFFFFFF;
      private static const PURPLE_LIGHT:uint = 0xE6E6FA;
      
      // 主界面组件
      private var mainPanel:Sprite;
      private var titleBar:Sprite;
      private var tabContainer:Sprite;
      private var contentContainer:Sprite;
      private var statusBar:Sprite;
      
      // 标签页
      private var tabs:Array = [];
      private var tabContents:Array = [];
      private var currentTab:int = 0;
      
      // 标签页名称和图标
      private var tabData:Array = [
         {name: "🎮角色", icon: "🎮"},
         {name: "⚔️装备", icon: "⚔️"},
         {name: "🐾宠物", icon: "🐾"},
         {name: "💎道具", icon: "💎"},
         {name: "🎯一键", icon: "🎯"},
         {name: "🔧工具", icon: "🔧"}
      ];
      
      // 界面状态
      private var isVisible:Boolean = false;
      private var isDragging:Boolean = false;
      private var dragOffset:Point;
      
      // 静态实例
      private static var instance:CuteGMPanel;
      
      public function CuteGMPanel()
      {
         super();
         this.initializePanel();
      }
      
      /**
       * 获取单例实例
       */
      public static function getInstance():CuteGMPanel
      {
         if (!instance) {
            instance = new CuteGMPanel();
         }
         return instance;
      }
      
      /**
       * 显示界面
       */
      public static function show():void
      {
         var panel:CuteGMPanel = getInstance();
         if (!panel.isVisible) {
            Main._stage.addChild(panel);
            panel.isVisible = true;
            panel.playShowAnimation();
         }
      }
      
      /**
       * 隐藏界面
       */
      public static function hide():void
      {
         var panel:CuteGMPanel = getInstance();
         if (panel.isVisible && panel.parent) {
            panel.parent.removeChild(panel);
            panel.isVisible = false;
         }
      }
      
      /**
       * 切换界面显示状态
       */
      public static function toggle():void
      {
         var panel:CuteGMPanel = getInstance();
         if (panel.isVisible) {
            hide();
         } else {
            show();
         }
      }
      
      /**
       * 初始化界面
       */
      private function initializePanel():void
      {
         this.createMainPanel();
         this.createTitleBar();
         this.createTabContainer();
         this.createContentContainer();
         this.createStatusBar();
         this.setupEventListeners();
      }
      
      /**
       * 创建主面板
       */
      private function createMainPanel():void
      {
         mainPanel = new Sprite();
         
         // 绘制主背景
         var bg:Graphics = mainPanel.graphics;
         bg.clear();
         
         // 添加阴影效果
         var shadow:DropShadowFilter = new DropShadowFilter(8, 45, 0x000000, 0.3, 8, 8, 1, 3);
         mainPanel.filters = [shadow];
         
         // 绘制圆角矩形背景
         var matrix:Matrix = new Matrix();
         matrix.createGradientBox(PANEL_WIDTH, PANEL_HEIGHT, Math.PI/2, 0, 0);
         bg.beginGradientFill(GradientType.LINEAR, [WHITE, PURPLE_LIGHT], [1, 1], [0, 255], matrix);
         bg.lineStyle(2, PINK_DARK, 1);
         bg.drawRoundRect(0, 0, PANEL_WIDTH, PANEL_HEIGHT, 20, 20);
         bg.endFill();
         
         // 设置位置（居中显示）
         mainPanel.x = (Main._stage.stageWidth - PANEL_WIDTH) / 2;
         mainPanel.y = (Main._stage.stageHeight - PANEL_HEIGHT) / 2;
         
         addChild(mainPanel);
      }
      
      /**
       * 创建标题栏
       */
      private function createTitleBar():void
      {
         titleBar = new Sprite();
         
         // 绘制标题栏背景
         var bg:Graphics = titleBar.graphics;
         var matrix:Matrix = new Matrix();
         matrix.createGradientBox(PANEL_WIDTH, 40, Math.PI/2, 0, 0);
         bg.beginGradientFill(GradientType.LINEAR, [PINK_MEDIUM, PINK_LIGHT], [1, 1], [0, 255], matrix);
         bg.drawRoundRect(0, 0, PANEL_WIDTH, 40, 20, 20);
         bg.endFill();
         
         // 添加标题文字
         var titleText:TextField = createTextField("🌸 可爱GM助手 🌸", 20, WHITE, true);
         titleText.x = 20;
         titleText.y = 10;
         titleBar.addChild(titleText);
         
         // 添加关闭按钮
         var closeBtn:Sprite = createCuteButton("✕", 30, 30, PINK_DARK);
         closeBtn.x = PANEL_WIDTH - 40;
         closeBtn.y = 5;
         closeBtn.addEventListener(MouseEvent.CLICK, onCloseClick);
         titleBar.addChild(closeBtn);
         
         // 添加最小化按钮
         var minBtn:Sprite = createCuteButton("－", 30, 30, PINK_MEDIUM);
         minBtn.x = PANEL_WIDTH - 75;
         minBtn.y = 5;
         minBtn.addEventListener(MouseEvent.CLICK, onMinimizeClick);
         titleBar.addChild(minBtn);
         
         mainPanel.addChild(titleBar);
      }
      
      /**
       * 创建可爱按钮
       */
      private function createCuteButton(text:String, width:Number, height:Number, color:uint):Sprite
      {
         var btn:Sprite = new Sprite();
         var bg:Graphics = btn.graphics;
         
         // 绘制圆角按钮背景
         bg.beginFill(color, 0.8);
         bg.lineStyle(1, WHITE, 0.8);
         bg.drawRoundRect(0, 0, width, height, 8, 8);
         bg.endFill();
         
         // 添加按钮文字
         var btnText:TextField = createTextField(text, 14, WHITE, true);
         btnText.x = (width - btnText.textWidth) / 2;
         btnText.y = (height - btnText.textHeight) / 2;
         btn.addChild(btnText);
         
         // 设置按钮属性
         btn.buttonMode = true;
         btn.useHandCursor = true;
         
         // 添加悬停效果
         btn.addEventListener(MouseEvent.MOUSE_OVER, onButtonOver);
         btn.addEventListener(MouseEvent.MOUSE_OUT, onButtonOut);
         
         return btn;
      }
      
      /**
       * 创建文本字段
       */
      private function createTextField(text:String, size:Number, color:uint, bold:Boolean = false):TextField
      {
         var tf:TextField = new TextField();
         var format:TextFormat = new TextFormat();
         format.font = "Arial";
         format.size = size;
         format.color = color;
         format.bold = bold;
         
         tf.defaultTextFormat = format;
         tf.text = text;
         tf.autoSize = TextFieldAutoSize.LEFT;
         tf.selectable = false;
         tf.mouseEnabled = false;
         
         return tf;
      }
      
      /**
       * 按钮悬停效果
       */
      private function onButtonOver(e:MouseEvent):void
      {
         var btn:Sprite = e.target as Sprite;
         btn.scaleX = btn.scaleY = 1.1;
         btn.alpha = 1.0;
      }
      
      private function onButtonOut(e:MouseEvent):void
      {
         var btn:Sprite = e.target as Sprite;
         btn.scaleX = btn.scaleY = 1.0;
         btn.alpha = 0.8;
      }
      
      /**
       * 关闭按钮点击事件
       */
      private function onCloseClick(e:MouseEvent):void
      {
         hide();
      }
      
      /**
       * 最小化按钮点击事件
       */
      private function onMinimizeClick(e:MouseEvent):void
      {
         hide();
      }
      
      /**
       * 播放显示动画
       */
      private function playShowAnimation():void
      {
         this.alpha = 0;
         this.scaleX = this.scaleY = 0.8;
         
         // 简单的淡入和缩放动画
         addEventListener(Event.ENTER_FRAME, onShowAnimation);
      }
      
      private function onShowAnimation(e:Event):void
      {
         this.alpha += 0.1;
         this.scaleX = this.scaleY += 0.02;
         
         if (this.alpha >= 1.0) {
            this.alpha = 1.0;
            this.scaleX = this.scaleY = 1.0;
            removeEventListener(Event.ENTER_FRAME, onShowAnimation);
         }
      }
      
      /**
       * 设置事件监听器
       */
      private function setupEventListeners():void
      {
         // 标题栏拖拽功能
         titleBar.addEventListener(MouseEvent.MOUSE_DOWN, onTitleMouseDown);
         Main._stage.addEventListener(MouseEvent.MOUSE_UP, onStageMouseUp);
         Main._stage.addEventListener(MouseEvent.MOUSE_MOVE, onStageMouseMove);
      }
      
      /**
       * 标题栏拖拽开始
       */
      private function onTitleMouseDown(e:MouseEvent):void
      {
         isDragging = true;
         dragOffset = new Point(e.stageX - mainPanel.x, e.stageY - mainPanel.y);
      }
      
      /**
       * 拖拽结束
       */
      private function onStageMouseUp(e:MouseEvent):void
      {
         isDragging = false;
      }
      
      /**
       * 拖拽移动
       */
      private function onStageMouseMove(e:MouseEvent):void
      {
         if (isDragging) {
            mainPanel.x = e.stageX - dragOffset.x;
            mainPanel.y = e.stageY - dragOffset.y;
         }
      }

      /**
       * 创建标签页容器
       */
      private function createTabContainer():void
      {
         tabContainer = new Sprite();
         tabContainer.y = 40; // 标题栏下方

         // 创建标签页
         for (var i:int = 0; i < tabData.length; i++) {
            var tab:Sprite = createTab(tabData[i].name, i);
            tab.x = i * 130;
            tabContainer.addChild(tab);
            tabs.push(tab);
         }

         // 设置第一个标签为激活状态
         setActiveTab(0);

         mainPanel.addChild(tabContainer);
      }

      /**
       * 创建单个标签页
       */
      private function createTab(name:String, index:int):Sprite
      {
         var tab:Sprite = new Sprite();
         var bg:Graphics = tab.graphics;

         // 绘制标签背景
         bg.beginFill(PINK_LIGHT, 0.6);
         bg.lineStyle(1, PINK_DARK, 0.8);
         bg.drawRoundRect(0, 0, 125, TAB_HEIGHT, 10, 10);
         bg.endFill();

         // 添加标签文字
         var tabText:TextField = createTextField(name, 14, PINK_DARK, true);
         tabText.x = (125 - tabText.textWidth) / 2;
         tabText.y = (TAB_HEIGHT - tabText.textHeight) / 2;
         tab.addChild(tabText);

         // 设置标签属性
         tab.buttonMode = true;
         tab.useHandCursor = true;
         tab.name = "tab_" + index;

         // 添加点击事件
         tab.addEventListener(MouseEvent.CLICK, onTabClick);
         tab.addEventListener(MouseEvent.MOUSE_OVER, onTabOver);
         tab.addEventListener(MouseEvent.MOUSE_OUT, onTabOut);

         return tab;
      }

      /**
       * 标签页点击事件
       */
      private function onTabClick(e:MouseEvent):void
      {
         var tab:Sprite = e.target as Sprite;
         var index:int = parseInt(tab.name.split("_")[1]);
         setActiveTab(index);
      }

      /**
       * 标签页悬停效果
       */
      private function onTabOver(e:MouseEvent):void
      {
         var tab:Sprite = e.target as Sprite;
         var index:int = parseInt(tab.name.split("_")[1]);
         if (index != currentTab) {
            tab.alpha = 0.8;
         }
      }

      private function onTabOut(e:MouseEvent):void
      {
         var tab:Sprite = e.target as Sprite;
         var index:int = parseInt(tab.name.split("_")[1]);
         if (index != currentTab) {
            tab.alpha = 0.6;
         }
      }

      /**
       * 设置激活标签
       */
      private function setActiveTab(index:int):void
      {
         // 重置所有标签样式
         for (var i:int = 0; i < tabs.length; i++) {
            var tab:Sprite = tabs[i];
            var bg:Graphics = tab.graphics;
            bg.clear();

            if (i == index) {
               // 激活状态
               bg.beginFill(PINK_MEDIUM, 1.0);
               bg.lineStyle(2, PINK_DARK, 1.0);
               tab.alpha = 1.0;
            } else {
               // 非激活状态
               bg.beginFill(PINK_LIGHT, 0.6);
               bg.lineStyle(1, PINK_DARK, 0.8);
               tab.alpha = 0.6;
            }
            bg.drawRoundRect(0, 0, 125, TAB_HEIGHT, 10, 10);
            bg.endFill();
         }

         currentTab = index;
         showTabContent(index);
      }

      /**
       * 创建内容容器
       */
      private function createContentContainer():void
      {
         contentContainer = new Sprite();
         contentContainer.y = 40 + TAB_HEIGHT; // 标题栏和标签页下方

         // 绘制内容区域背景
         var bg:Graphics = contentContainer.graphics;
         bg.beginFill(WHITE, 0.9);
         bg.lineStyle(1, PINK_LIGHT, 0.8);
         bg.drawRoundRect(10, 0, PANEL_WIDTH - 20, CONTENT_HEIGHT, 10, 10);
         bg.endFill();

         // 创建所有标签页内容
         this.createAllTabContents();

         mainPanel.addChild(contentContainer);
      }

      /**
       * 创建状态栏
       */
      private function createStatusBar():void
      {
         statusBar = new Sprite();
         statusBar.y = PANEL_HEIGHT - 30;

         // 绘制状态栏背景
         var bg:Graphics = statusBar.graphics;
         bg.beginFill(PURPLE_LIGHT, 0.8);
         bg.drawRoundRect(0, 0, PANEL_WIDTH, 30, 0, 0);
         bg.endFill();

         // 添加状态文字
         var statusText:TextField = createTextField("就绪 | 按P键打开/关闭GM界面", 12, PINK_DARK);
         statusText.x = 20;
         statusText.y = 8;
         statusBar.addChild(statusText);

         mainPanel.addChild(statusBar);
      }

      /**
       * 创建所有标签页内容
       */
      private function createAllTabContents():void
      {
         tabContents = [];

         // 创建6个标签页内容
         for (var i:int = 0; i < 6; i++) {
            var content:Sprite = new Sprite();
            content.x = 15;
            content.y = 10;
            content.visible = false;

            switch(i) {
               case 0: // 角色管理
                  this.createCharacterContent(content);
                  break;
               case 1: // 装备管理
                  this.createEquipmentContent(content);
                  break;
               case 2: // 宠物管理
                  this.createPetContent(content);
                  break;
               case 3: // 道具管理
                  this.createItemContent(content);
                  break;
               case 4: // 一键功能
                  this.createQuickContent(content);
                  break;
               case 5: // 工具箱
                  this.createToolContent(content);
                  break;
            }

            tabContents.push(content);
            contentContainer.addChild(content);
         }

         // 显示第一个标签页内容
         showTabContent(0);
      }

      /**
       * 显示指定标签页内容
       */
      private function showTabContent(index:int):void
      {
         for (var i:int = 0; i < tabContents.length; i++) {
            tabContents[i].visible = (i == index);
         }
      }

      /**
       * 创建角色管理内容
       */
      private function createCharacterContent(container:Sprite):void
      {
         var yPos:Number = 10;

         // 标题
         var title:TextField = createTextField("🎮 角色属性管理", 18, PINK_DARK, true);
         title.x = 10;
         title.y = yPos;
         container.addChild(title);
         yPos += 35;

         // 基础属性区域
         var basicGroup:Sprite = createGroup("基础属性", 350, 200);
         basicGroup.x = 10;
         basicGroup.y = yPos;
         container.addChild(basicGroup);

         // 等级设置
         var levelInput:TextField = createInputField("99", 80);
         levelInput.x = 20;
         levelInput.y = 30;
         basicGroup.addChild(levelInput);

         var levelBtn:Sprite = createCuteButton("设置等级", 80, 25, PINK_MEDIUM);
         levelBtn.x = 110;
         levelBtn.y = 30;
         levelBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            setPlayerLevel(parseInt(levelInput.text));
         });
         basicGroup.addChild(levelBtn);

         // 金币设置
         var goldInput:TextField = createInputField("999999999", 80);
         goldInput.x = 20;
         goldInput.y = 65;
         basicGroup.addChild(goldInput);

         var goldBtn:Sprite = createCuteButton("设置金币", 80, 25, PINK_MEDIUM);
         goldBtn.x = 110;
         goldBtn.y = 65;
         goldBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            setPlayerGold(parseInt(goldInput.text));
         });
         basicGroup.addChild(goldBtn);

         // 技能点设置
         var skillInput:TextField = createInputField("999", 80);
         skillInput.x = 20;
         skillInput.y = 100;
         basicGroup.addChild(skillInput);

         var skillBtn:Sprite = createCuteButton("设置技能点", 80, 25, PINK_MEDIUM);
         skillBtn.x = 110;
         skillBtn.y = 100;
         skillBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            setPlayerSkillPoints(parseInt(skillInput.text));
         });
         basicGroup.addChild(skillBtn);

         // 击杀点设置
         var killInput:TextField = createInputField("999999", 80);
         killInput.x = 20;
         killInput.y = 135;
         basicGroup.addChild(killInput);

         var killBtn:Sprite = createCuteButton("设置击杀点", 80, 25, PINK_MEDIUM);
         killBtn.x = 110;
         killBtn.y = 135;
         killBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            setPlayerKillPoints(parseInt(killInput.text));
         });
         basicGroup.addChild(killBtn);

         // 快捷功能区域
         var quickGroup:Sprite = createGroup("快捷功能", 350, 200);
         quickGroup.x = 380;
         quickGroup.y = yPos;
         container.addChild(quickGroup);

         // 解锁功能按钮
         var unlockBtns:Array = [
            {text: "背包解锁", func: unlockBag},
            {text: "宠栏解锁", func: unlockPetSlot},
            {text: "精灵槽解锁", func: unlockElvesSlot},
            {text: "特殊栏解锁", func: unlockSpecialSlot},
            {text: "关卡解锁", func: unlockStages},
            {text: "图鉴添加", func: addAllCards}
         ];

         for (var i:int = 0; i < unlockBtns.length; i++) {
            var btn:Sprite = createCuteButton(unlockBtns[i].text, 100, 25, PINK_MEDIUM);
            btn.x = 20 + (i % 2) * 110;
            btn.y = 30 + Math.floor(i / 2) * 35;
            btn.addEventListener(MouseEvent.CLICK, unlockBtns[i].func);
            quickGroup.addChild(btn);
         }

         yPos += 220;

         // 成就和其他功能
         var achieveGroup:Sprite = createGroup("成就与其他", 720, 100);
         achieveGroup.x = 10;
         achieveGroup.y = yPos;
         container.addChild(achieveGroup);

         var achieveBtns:Array = [
            {text: "成就全亮", func: unlockAllAchievements},
            {text: "过检测", func: bypassDetection},
            {text: "宝珠满级", func: maxBaoZhu},
            {text: "悬赏全满", func: maxWanted},
            {text: "四职业技能", func: allClassSkills}
         ];

         for (var j:int = 0; j < achieveBtns.length; j++) {
            var achieveBtn:Sprite = createCuteButton(achieveBtns[j].text, 100, 25, PINK_DARK);
            achieveBtn.x = 20 + j * 110;
            achieveBtn.y = 30;
            achieveBtn.addEventListener(MouseEvent.CLICK, achieveBtns[j].func);
            achieveGroup.addChild(achieveBtn);
         }
      }

      /**
       * 创建装备管理内容
       */
      private function createEquipmentContent(container:Sprite):void
      {
         var yPos:Number = 10;

         // 标题
         var title:TextField = createTextField("⚔️ 装备管理", 18, PINK_DARK, true);
         title.x = 10;
         title.y = yPos;
         container.addChild(title);
         yPos += 35;

         // 装备添加区域
         var equipGroup:Sprite = createGroup("装备添加", 350, 150);
         equipGroup.x = 10;
         equipGroup.y = yPos;
         container.addChild(equipGroup);

         // 装备ID输入
         var equipIdInput:TextField = createInputField("装备ID", 100);
         equipIdInput.x = 20;
         equipIdInput.y = 30;
         equipGroup.addChild(equipIdInput);

         // 数量输入
         var equipCountInput:TextField = createInputField("1", 60);
         equipCountInput.x = 130;
         equipCountInput.y = 30;
         equipGroup.addChild(equipCountInput);

         // 添加按钮
         var addEquipBtn:Sprite = createCuteButton("添加装备", 80, 25, PINK_MEDIUM);
         addEquipBtn.x = 200;
         addEquipBtn.y = 30;
         addEquipBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            addEquipment(parseInt(equipIdInput.text), parseInt(equipCountInput.text));
         });
         equipGroup.addChild(addEquipBtn);

         // 一键装备按钮
         var oneKeyEquipBtn:Sprite = createCuteButton("一键装备", 120, 30, PINK_DARK);
         oneKeyEquipBtn.x = 20;
         oneKeyEquipBtn.y = 70;
         oneKeyEquipBtn.addEventListener(MouseEvent.CLICK, oneKeyEquipment);
         equipGroup.addChild(oneKeyEquipBtn);

         // 祝福功能区域
         var blessGroup:Sprite = createGroup("祝福系统", 350, 150);
         blessGroup.x = 380;
         blessGroup.y = yPos;
         container.addChild(blessGroup);

         // 祝福界面按钮
         var blessBtns:Array = [
            "祝福界面1", "祝福界面2", "祝福界面3", "祝福界面4",
            "祝福界面5", "祝福界面6", "星灵王祝福"
         ];

         for (var i:int = 0; i < blessBtns.length; i++) {
            var blessBtn:Sprite = createCuteButton(blessBtns[i], 80, 20, PINK_LIGHT);
            blessBtn.x = 20 + (i % 3) * 85;
            blessBtn.y = 30 + Math.floor(i / 3) * 25;
            blessBtn.name = "bless_" + (i + 1);
            blessBtn.addEventListener(MouseEvent.CLICK, onBlessClick);
            blessGroup.addChild(blessBtn);
         }

         yPos += 170;

         // 自定义装备区域
         var customGroup:Sprite = createGroup("自定义装备", 720, 200);
         customGroup.x = 10;
         customGroup.y = yPos;
         container.addChild(customGroup);

         // 自定义装备说明
         var customText:TextField = createTextField("点击下方按钮打开自定义装备界面", 14, PINK_DARK);
         customText.x = 20;
         customText.y = 30;
         customGroup.addChild(customText);

         var customBtn:Sprite = createCuteButton("打开自定义装备", 150, 30, PINK_DARK);
         customBtn.x = 20;
         customBtn.y = 60;
         customBtn.addEventListener(MouseEvent.CLICK, openCustomEquipment);
         customGroup.addChild(customBtn);
      }

      /**
       * 创建分组容器
       */
      private function createGroup(title:String, width:Number, height:Number):Sprite
      {
         var group:Sprite = new Sprite();
         var bg:Graphics = group.graphics;

         // 绘制分组背景
         bg.beginFill(PURPLE_LIGHT, 0.3);
         bg.lineStyle(1, PINK_MEDIUM, 0.8);
         bg.drawRoundRect(0, 0, width, height, 8, 8);
         bg.endFill();

         // 添加标题
         var titleText:TextField = createTextField(title, 14, PINK_DARK, true);
         titleText.x = 10;
         titleText.y = 5;
         group.addChild(titleText);

         return group;
      }

      /**
       * 创建输入框
       */
      private function createInputField(defaultText:String, width:Number):TextField
      {
         var input:TextField = new TextField();
         input.width = width;
         input.height = 25;
         input.background = true;
         input.backgroundColor = WHITE;
         input.border = true;
         input.borderColor = PINK_MEDIUM;
         input.type = TextFieldType.INPUT;
         input.textColor = 0x333333;
         input.text = defaultText;

         var format:TextFormat = new TextFormat();
         format.font = "Arial";
         format.size = 12;
         input.defaultTextFormat = format;

         return input;
      }

      /**
       * 显示成功消息
       */
      private function showSuccessMessage(message:String):void
      {
         NewMC.Open("文字提示", Main._stage, 400, 400, 30, 0, true, 2, "✨ " + message + " ✨");

         // 更新状态栏
         updateStatusBar("操作成功: " + message);
      }

      /**
       * 更新状态栏信息
       */
      private function updateStatusBar(message:String):void
      {
         if (statusBar && statusBar.numChildren > 0) {
            var statusText:TextField = statusBar.getChildAt(0) as TextField;
            if (statusText) {
               statusText.text = message;

               // 添加状态栏闪烁效果
               statusText.alpha = 0.5;
               statusText.addEventListener(Event.ENTER_FRAME, onStatusFlash);
            }
         }
      }

      /**
       * 状态栏闪烁效果
       */
      private function onStatusFlash(e:Event):void
      {
         var statusText:TextField = e.target as TextField;
         statusText.alpha += 0.1;
         if (statusText.alpha >= 1.0) {
            statusText.alpha = 1.0;
            statusText.removeEventListener(Event.ENTER_FRAME, onStatusFlash);

            // 3秒后恢复默认状态
            setTimeout(function():void {
               if (statusText) {
                  statusText.text = "就绪 | 按P键打开/关闭GM界面";
               }
            }, 3000);
         }
      }

      /**
       * 简单的setTimeout实现
       */
      private function setTimeout(func:Function, delay:Number):void
      {
         var timer:Timer = new Timer(delay, 1);
         timer.addEventListener(TimerEvent.TIMER, function(e:TimerEvent):void {
            func();
            timer.removeEventListener(TimerEvent.TIMER, arguments.callee);
         });
         timer.start();
      }

      // ==================== 角色管理功能实现 ====================

      /**
       * 设置玩家等级
       */
      private function setPlayerLevel(level:int):void
      {
         Main.player1.level = VT.createVT(level);
         if (Main.player2) {
            Main.player2.level = VT.createVT(level);
         }
         showSuccessMessage("等级设置成功：" + level);
      }

      /**
       * 设置玩家金币
       */
      private function setPlayerGold(gold:int):void
      {
         Main.player1.gold.setValue(gold);
         if (Main.player2) {
            Main.player2.gold.setValue(gold);
         }
         showSuccessMessage("金币设置成功：" + gold);
      }

      /**
       * 设置技能点
       */
      private function setPlayerSkillPoints(points:int):void
      {
         Main.player_1.data.points.setValue(points);
         if (Main.player_2) {
            Main.player_2.data.points.setValue(points);
         }
         showSuccessMessage("技能点设置成功：" + points);
      }

      /**
       * 设置击杀点
       */
      private function setPlayerKillPoints(points:int):void
      {
         Main.player1.killPoint.setValue(points);
         if (Main.player2) {
            Main.player2.killPoint.setValue(points);
         }
         showSuccessMessage("击杀点设置成功：" + points);
      }

      /**
       * 解锁背包
       */
      private function unlockBag(e:MouseEvent):void
      {
         for (var i:int = 0; i < 50; i++) {
            Main.player1.getBag().bagOpen[i] = true;
         }
         showSuccessMessage("背包已全部解锁！");
      }

      /**
       * 解锁宠物栏
       */
      private function unlockPetSlot(e:MouseEvent):void
      {
         for (var i:int = 0; i < 50; i++) {
            Main.player1.getPetSlot().slotOpen[i] = true;
         }
         showSuccessMessage("宠物栏已全部解锁！");
      }

      /**
       * 解锁精灵槽
       */
      private function unlockElvesSlot(e:MouseEvent):void
      {
         for (var i:int = 0; i < 50; i++) {
            Main.player1.getElvesSlot().slotOpen[i] = true;
         }
         showSuccessMessage("精灵槽已全部解锁！");
      }

      /**
       * 解锁特殊栏
       */
      private function unlockSpecialSlot(e:MouseEvent):void
      {
         for (var i:int = 0; i < 50; i++) {
            Main.player1.getSpecialSlot().slotOpen[i] = true;
         }
         showSuccessMessage("特殊栏已全部解锁！");
      }

      /**
       * 解锁关卡
       */
      private function unlockStages(e:MouseEvent):void
      {
         for (var i:int = 0; i < 150; i++) {
            Main.guanKa[i] = 3;
         }
         showSuccessMessage("所有关卡已解锁！");
      }

      /**
       * 添加图鉴
       */
      private function addAllCards(e:MouseEvent):void
      {
         for (var i:int = 0; i < 12000; i++) {
            CardPanel.monsterSlot.addMonsterSlot(i);
         }
         showSuccessMessage("图鉴添加成功！");
      }

      /**
       * 解锁所有成就
       */
      private function unlockAllAchievements(e:MouseEvent):void
      {
         var myXml:XML = XMLAsset.createXML(Data2.AchNum);
         for each(var property:XML in myXml.成就) {
            var id:Number = Number(property.编号);
            var name:String = String(property.名字);
            var frame:Number = Number(property.帧数);
            var sm:String = String(property.说明);
            var everyDady:Boolean = (property.是否每日.toString() == "true") as Boolean;
            var numType:Number = Number(property.类型);
            var rewardAc:Number = Number(property.奖励成就点);
            var goodsId:String = String(property.指定id);
            var ry:Boolean = (property.是否同时.toString() == "true") as Boolean;
            var goodsType:String = String(property.id类型);
            var nType:Number = Number(property.获取方式);
            var data:AchNumBasicData = AchNumBasicData.ceartAchNum(id, name, frame, sm, everyDady, numType, rewardAc, goodsId, goodsType, 2, -1, -1, ry, nType);
            AchNumFactory.allData.push(data);
         }
         showSuccessMessage("所有成就已点亮！");
      }

      /**
       * 过检测
       */
      private function bypassDetection(e:MouseEvent):void
      {
         // 实现过检测功能
         showSuccessMessage("检测已绕过！");
      }

      /**
       * 宝珠满级
       */
      private function maxBaoZhu(e:MouseEvent):void
      {
         Panel_youling.lvArr = [100, 100, 100, 100, 100];
         Panel_youling.bzNumArr = [2147483647, 2147483647, 2147483647, 2147483647, 2147483647];
         showSuccessMessage("宝珠已满级！");
      }

      /**
       * 悬赏全满
       */
      private function maxWanted(e:MouseEvent):void
      {
         // 实现悬赏全满功能
         showSuccessMessage("悬赏已全满！");
      }

      /**
       * 四职业技能
       */
      private function allClassSkills(e:MouseEvent):void
      {
         // 实现四职业技能功能
         showSuccessMessage("四职业技能已获得！");
      }

      // ==================== 装备管理功能实现 ====================

      /**
       * 添加装备
       */
      private function addEquipment(equipId:int, count:int):void
      {
         for (var i:int = 0; i < count; i++) {
            Main.player_1.data.getBag().addEquipBag(EquipFactory.createEquipByID(equipId));
         }
         showSuccessMessage("装备添加成功：ID " + equipId + " x" + count);
      }

      /**
       * 一键装备
       */
      private function oneKeyEquipment(e:MouseEvent):void
      {
         // 调用原有的一键装备功能
         var gm:GM = new GM();
         gm.一键装备(null);
         showSuccessMessage("一键装备完成！");
      }

      /**
       * 祝福界面点击事件
       */
      private function onBlessClick(e:MouseEvent):void
      {
         var btn:Sprite = e.target as Sprite;
         var index:int = parseInt(btn.name.split("_")[1]);

         switch(index) {
            case 1: JinHuaPanel.open(true, 1, 4); break;
            case 2: JinHuaPanel.open(true, 2, 3); break;
            case 3: JinHuaPanel.open(true, 3, 1); break;
            case 4: JinHuaPanel.open(true, 4, 2); break;
            case 5: JinHuaPanel.open(true, 5, 5); break;
            case 6: JinHuaPanel.open(true, 6, 8); break;
            case 7: JinHuaPanel2.open(true); break;
         }
         showSuccessMessage("祝福界面已打开");
      }

      /**
       * 打开自定义装备界面
       */
      private function openCustomEquipment(e:MouseEvent):void
      {
         // 这里可以打开原有的自定义装备界面
         showSuccessMessage("自定义装备界面功能开发中...");
      }

      // ==================== 其他标签页内容创建 ====================

      /**
       * 创建宠物管理内容
       */
      private function createPetContent(container:Sprite):void
      {
         var yPos:Number = 10;

         // 标题
         var title:TextField = createTextField("🐾 宠物管理", 18, PINK_DARK, true);
         title.x = 10;
         title.y = yPos;
         container.addChild(title);
         yPos += 35;

         // 一键宠物按钮
         var oneKeyPetBtn:Sprite = createCuteButton("🐾 一键宠物", 150, 40, PINK_DARK);
         oneKeyPetBtn.x = 50;
         oneKeyPetBtn.y = yPos;
         oneKeyPetBtn.addEventListener(MouseEvent.CLICK, oneKeyPets);
         container.addChild(oneKeyPetBtn);

         // 宠物装备按钮
         var petEquipBtn:Sprite = createCuteButton("🎒 一键宠物装备", 150, 40, PINK_MEDIUM);
         petEquipBtn.x = 220;
         petEquipBtn.y = yPos;
         petEquipBtn.addEventListener(MouseEvent.CLICK, oneKeyPetEquip);
         container.addChild(petEquipBtn);

         yPos += 60;

         // 清空功能
         var clearPetBtn:Sprite = createCuteButton("清空宠物", 120, 30, 0xFF6B6B);
         clearPetBtn.x = 50;
         clearPetBtn.y = yPos;
         clearPetBtn.addEventListener(MouseEvent.CLICK, clearPets);
         container.addChild(clearPetBtn);
      }

      /**
       * 创建道具管理内容
       */
      private function createItemContent(container:Sprite):void
      {
         var yPos:Number = 10;

         // 标题
         var title:TextField = createTextField("💎 道具管理", 18, PINK_DARK, true);
         title.x = 10;
         title.y = yPos;
         container.addChild(title);
         yPos += 35;

         // 一键功能按钮
         var itemBtns:Array = [
            {text: "💎 一键道具", func: oneKeyItems},
            {text: "🧪 一键消耗品", func: oneKeySupplies},
            {text: "💍 一键技能石", func: oneKeySkillStones},
            {text: "🏆 一键称号", func: oneKeyTitles},
            {text: "✨ 一键精灵", func: oneKeyElves}
         ];

         for (var i:int = 0; i < itemBtns.length; i++) {
            var btn:Sprite = createCuteButton(itemBtns[i].text, 140, 35, PINK_MEDIUM);
            btn.x = 50 + (i % 3) * 160;
            btn.y = yPos + Math.floor(i / 3) * 50;
            btn.addEventListener(MouseEvent.CLICK, itemBtns[i].func);
            container.addChild(btn);
         }

         yPos += 120;

         // 清空功能
         var clearBtns:Array = [
            {text: "清空背包", func: clearBag},
            {text: "清空仓库", func: clearStorage},
            {text: "清空精灵", func: clearElves},
            {text: "清空称号", func: clearTitles}
         ];

         for (var j:int = 0; j < clearBtns.length; j++) {
            var clearBtn:Sprite = createCuteButton(clearBtns[j].text, 120, 30, 0xFF6B6B);
            clearBtn.x = 50 + j * 140;
            clearBtn.y = yPos;
            clearBtn.addEventListener(MouseEvent.CLICK, clearBtns[j].func);
            container.addChild(clearBtn);
         }
      }

      /**
       * 创建一键功能内容
       */
      private function createQuickContent(container:Sprite):void
      {
         var yPos:Number = 10;

         // 标题
         var title:TextField = createTextField("🎯 一键功能", 18, PINK_DARK, true);
         title.x = 10;
         title.y = yPos;
         container.addChild(title);
         yPos += 35;

         // 一键功能按钮组
         var quickBtns:Array = [
            {text: "🎮 一键角色满级", func: quickMaxCharacter, color: PINK_DARK},
            {text: "⚔️ 一键装备", func: oneKeyEquipment, color: PINK_DARK},
            {text: "🐾 一键宠物", func: oneKeyPets, color: PINK_DARK},
            {text: "💎 一键道具", func: oneKeyItems, color: PINK_DARK},
            {text: "🏆 一键成就", func: unlockAllAchievements, color: PINK_DARK},
            {text: "🔓 一键解锁", func: quickUnlockAll, color: PINK_DARK}
         ];

         for (var i:int = 0; i < quickBtns.length; i++) {
            var btn:Sprite = createCuteButton(quickBtns[i].text, 200, 40, quickBtns[i].color);
            btn.x = 50 + (i % 3) * 220;
            btn.y = yPos + Math.floor(i / 3) * 60;
            btn.addEventListener(MouseEvent.CLICK, quickBtns[i].func);
            container.addChild(btn);
         }
      }

      /**
       * 创建工具箱内容
       */
      private function createToolContent(container:Sprite):void
      {
         var yPos:Number = 10;

         // 标题
         var title:TextField = createTextField("🔧 工具箱", 18, PINK_DARK, true);
         title.x = 10;
         title.y = yPos;
         container.addChild(title);
         yPos += 35;

         // 代码查询按钮
         var codeBtns:Array = [
            "装备代码", "道具代码", "宠物代码", "精灵代码",
            "称号代码", "宝石代码", "药品代码", "宠装代码"
         ];

         for (var i:int = 0; i < codeBtns.length; i++) {
            var btn:Sprite = createCuteButton(codeBtns[i], 120, 30, PINK_LIGHT);
            btn.x = 50 + (i % 4) * 140;
            btn.y = yPos + Math.floor(i / 4) * 40;
            btn.name = "code_" + i;
            btn.addEventListener(MouseEvent.CLICK, onCodeQuery);
            container.addChild(btn);
         }

         yPos += 100;

         // 代码显示区域
         var codeDisplay:TextField = new TextField();
         codeDisplay.x = 50;
         codeDisplay.y = yPos;
         codeDisplay.width = 650;
         codeDisplay.height = 200;
         codeDisplay.background = true;
         codeDisplay.backgroundColor = WHITE;
         codeDisplay.border = true;
         codeDisplay.borderColor = PINK_MEDIUM;
         codeDisplay.multiline = true;
         codeDisplay.wordWrap = true;
         codeDisplay.text = "点击上方按钮查询相应代码...";
         codeDisplay.name = "codeDisplay";
         container.addChild(codeDisplay);
      }

      // ==================== 宠物管理功能实现 ====================

      /**
       * 一键宠物
       */
      private function oneKeyPets(e:MouseEvent):void
      {
         var gm:GM = new GM();
         gm.一键宠物(null);
         showSuccessMessage("一键宠物完成！");
      }

      /**
       * 一键宠物装备
       */
      private function oneKeyPetEquip(e:MouseEvent):void
      {
         var gm:GM = new GM();
         gm.一键宠物装备(null);
         showSuccessMessage("一键宠物装备完成！");
      }

      /**
       * 清空宠物
       */
      private function clearPets(e:MouseEvent):void
      {
         for (var i:int = 0; i < 50; i++) {
            Main.player1.getPetSlot().slot[i] = null;
         }
         showSuccessMessage("所有宠物已清除！");
      }

      // ==================== 道具管理功能实现 ====================

      /**
       * 一键道具
       */
      private function oneKeyItems(e:MouseEvent):void
      {
         var gm:GM = new GM();
         gm.一键道具(null);
         showSuccessMessage("一键道具完成！");
      }

      /**
       * 一键消耗品
       */
      private function oneKeySupplies(e:MouseEvent):void
      {
         var gm:GM = new GM();
         gm.一键消耗品(null);
         showSuccessMessage("一键消耗品完成！");
      }

      /**
       * 一键技能石
       */
      private function oneKeySkillStones(e:MouseEvent):void
      {
         var gm:GM = new GM();
         gm.一键技能石(null);
         showSuccessMessage("一键技能石完成！");
      }

      /**
       * 一键称号
       */
      private function oneKeyTitles(e:MouseEvent):void
      {
         var gm:GM = new GM();
         gm.一键称号(null);
         showSuccessMessage("一键称号完成！");
      }

      /**
       * 一键精灵
       */
      private function oneKeyElves(e:MouseEvent):void
      {
         var gm:GM = new GM();
         gm.一键精灵(null);
         showSuccessMessage("一键精灵完成！");
      }

      /**
       * 清空背包
       */
      private function clearBag(e:MouseEvent):void
      {
         for (var i:int = 0; i < 35; i++) {
            Main.player1.getBag().equipBag[i] = null;
            Main.player1.getBag().suppliesBag[i] = null;
            Main.player_1.data.getBag().gemBag[i] = null;
            Main.player1.getBag().otherobjBag[i] = null;
            Main.player_1.data.getBag().questBag[i] = null;
         }
         showSuccessMessage("背包已清空！");
      }

      /**
       * 清空仓库
       */
      private function clearStorage(e:MouseEvent):void
      {
         for (var i:int = 0; i < 35; i++) {
            StoragePanel.storage.equipStorage[i] = null;
            StoragePanel.storage.suppliesStorage[i] = null;
            StoragePanel.storage.gemStorage[i] = null;
            StoragePanel.storage.otherobjStorage[i] = null;
         }
         showSuccessMessage("仓库已清空！");
      }

      /**
       * 清空精灵
       */
      private function clearElves(e:MouseEvent):void
      {
         for (var i:int = 0; i < 50; i++) {
            Main.player1.getElvesSlot().slot[i] = null;
         }
         showSuccessMessage("所有精灵已清除！");
      }

      /**
       * 清空称号
       */
      private function clearTitles(e:MouseEvent):void
      {
         for (var i:int = 0; i < 50; i++) {
            Main.player1.getTitleSlot().slot[i] = null;
         }
         showSuccessMessage("所有称号已清除！");
      }

      // ==================== 一键功能实现 ====================

      /**
       * 一键角色满级
       */
      private function quickMaxCharacter(e:MouseEvent):void
      {
         setPlayerLevel(99);
         setPlayerGold(999999999);
         setPlayerSkillPoints(999);
         setPlayerKillPoints(999999);
         showSuccessMessage("角色属性已满级！");
      }

      /**
       * 一键解锁全部
       */
      private function quickUnlockAll(e:MouseEvent):void
      {
         unlockBag(null);
         unlockPetSlot(null);
         unlockElvesSlot(null);
         unlockSpecialSlot(null);
         unlockStages(null);
         showSuccessMessage("所有功能已解锁！");
      }

      // ==================== 工具箱功能实现 ====================

      /**
       * 代码查询点击事件
       */
      private function onCodeQuery(e:MouseEvent):void
      {
         var btn:Sprite = e.target as Sprite;
         var index:int = parseInt(btn.name.split("_")[1]);
         var display:TextField = btn.parent.getChildByName("codeDisplay") as TextField;

         var gm:GM = new GM();
         gm.yj14 = display; // 设置显示文本框

         switch(index) {
            case 0: gm.装备代码(null); break;
            case 1: gm.道具代码(null); break;
            case 2: gm.宠物代码(null); break;
            case 3: gm.精灵代码(null); break;
            case 4: gm.称号代码(null); break;
            case 5: gm.宝石代码(null); break;
            case 6: gm.药品代码(null); break;
            case 7: gm.宠装代码(null); break;
         }
      }
   }
}
